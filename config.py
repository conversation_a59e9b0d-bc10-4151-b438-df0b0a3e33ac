#!/usr/bin/env python3
"""
全局环境变量配置文件

统一管理所有脚本的环境变量配置
"""

import os
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class Config:
    """全局配置类"""

    def __init__(self):
        """初始化配置"""
        self._load_config()

    def _load_config(self):
        """加载配置"""
        # 数据库配置 - 默认使用106服务器
        self.DB_CONFIG = {
            "host": os.getenv("DB_HOST", "***********"),
            "port": int(os.getenv("DB_PORT", "5432")),
            "database": os.getenv("DB_NAME", "product"),
            "user": os.getenv("DB_USER", "username"),
            "password": os.getenv("DB_PASSWORD", "password"),
        }

        # API配置
        self.API_CONFIG = {
            "base_url": os.getenv("ZKMALL_API_BASE", "https://zkmall.zktecoiot.com"),
            "username": os.getenv("ZKMALL_USERNAME", "18929343717"),
            "password": os.getenv("ZKMALL_PASSWORD", ""),
            "timeout": int(os.getenv("API_TIMEOUT", "30")),
            "retry_count": int(os.getenv("API_RETRY_COUNT", "3")),
        }

        # 日志配置
        self.LOG_CONFIG = {
            "level": os.getenv("LOG_LEVEL", "INFO"),
            "format": os.getenv(
                "LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            ),
            "file": os.getenv("LOG_FILE", ""),
            "max_size": int(os.getenv("LOG_MAX_SIZE", "10485760")),  # 10MB
            "backup_count": int(os.getenv("LOG_BACKUP_COUNT", "5")),
        }

        # 同步配置
        self.SYNC_CONFIG = {
            "interval": int(os.getenv("SYNC_INTERVAL", "3600")),  # 1小时
            "batch_size": int(os.getenv("SYNC_BATCH_SIZE", "1000")),
            "max_retries": int(os.getenv("SYNC_MAX_RETRIES", "3")),
            "timeout": int(os.getenv("SYNC_TIMEOUT", "300")),  # 5分钟
        }

        # FastGPT配置
        self.FASTGPT_CONFIG = {
            "api_base": os.getenv("FASTGPT_API_BASE", "https://api.fastgpt.in"),
            "api_key": os.getenv("FASTGPT_API_KEY", ""),
            "timeout": int(os.getenv("FASTGPT_TIMEOUT", "30")),
            "max_retries": int(os.getenv("FASTGPT_MAX_RETRIES", "3")),
        }

        # 系统配置
        self.SYSTEM_CONFIG = {
            "debug": os.getenv("DEBUG", "False").lower() == "true",
            "environment": os.getenv("ENVIRONMENT", "development"),
            "timezone": os.getenv("TIMEZONE", "Asia/Shanghai"),
            "encoding": os.getenv("ENCODING", "utf-8"),
        }

    def get_db_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.DB_CONFIG.copy()

    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.API_CONFIG.copy()

    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.LOG_CONFIG.copy()

    def get_sync_config(self) -> Dict[str, Any]:
        """获取同步配置"""
        return self.SYNC_CONFIG.copy()

    def get_fastgpt_config(self) -> Dict[str, Any]:
        """获取FastGPT配置"""
        return self.FASTGPT_CONFIG.copy()

    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self.SYSTEM_CONFIG.copy()

    def validate_config(self) -> bool:
        """验证配置完整性"""
        errors = []

        # 验证数据库配置
        if not self.DB_CONFIG["password"]:
            errors.append("数据库密码未设置 (DB_PASSWORD)")

        if not self.DB_CONFIG["host"]:
            errors.append("数据库主机未设置 (DB_HOST)")

        # 验证API配置
        if not self.API_CONFIG["password"]:
            errors.append("API密码未设置 (ZKMALL_PASSWORD)")

        # 验证FastGPT配置（可选）
        if not self.FASTGPT_CONFIG["api_key"]:
            logger.warning(
                "FastGPT API密钥未设置 (FASTGPT_API_KEY) - FastGPT功能将不可用"
            )

        # 输出错误信息
        if errors:
            for error in errors:
                logger.error(f"配置验证失败: {error}")
            return False

        logger.info("配置验证通过")
        return True

    def print_config_summary(self):
        """打印配置摘要"""
        print("=" * 50)
        print("📋 系统配置摘要")
        print("=" * 50)

        print(
            f"🗄️  数据库: {self.DB_CONFIG['user']}@{self.DB_CONFIG['host']}:{self.DB_CONFIG['port']}/{self.DB_CONFIG['database']}"
        )
        print(f"🌐 API地址: {self.API_CONFIG['base_url']}")
        print(f"👤 API用户: {self.API_CONFIG['username']}")
        print(f"🤖 FastGPT地址: {self.FASTGPT_CONFIG['api_base']}")
        print(f"📝 日志级别: {self.LOG_CONFIG['level']}")
        print(f"🔄 同步间隔: {self.SYNC_CONFIG['interval']}秒")
        print(f"🏷️  环境: {self.SYSTEM_CONFIG['environment']}")
        print(f"🐛 调试模式: {'开启' if self.SYSTEM_CONFIG['debug'] else '关闭'}")

        # 检查敏感信息
        sensitive_missing = []
        if not self.DB_CONFIG["password"]:
            sensitive_missing.append("DB_PASSWORD")
        if not self.API_CONFIG["password"]:
            sensitive_missing.append("ZKMALL_PASSWORD")

        if sensitive_missing:
            print(f"⚠️  缺少敏感配置: {', '.join(sensitive_missing)}")
        else:
            print("✅ 所有必要配置已设置")

        print("=" * 50)


# 创建全局配置实例
config = Config()


def get_db_connection_string() -> str:
    """获取数据库连接字符串"""
    db_config = config.get_db_config()
    return f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"


def setup_logging():
    """设置日志配置"""
    log_config = config.get_log_config()

    # 设置日志级别
    level = getattr(logging, log_config["level"].upper(), logging.INFO)

    # 配置日志格式
    formatter = logging.Formatter(log_config["format"])

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # 清除现有处理器
    root_logger.handlers.clear()

    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 如果指定了日志文件，添加文件处理器
    if log_config["file"]:
        from logging.handlers import RotatingFileHandler

        file_handler = RotatingFileHandler(
            log_config["file"],
            maxBytes=log_config["max_size"],
            backupCount=log_config["backup_count"],
            encoding="utf-8",
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    logger.info("日志系统初始化完成")


def load_env_file(env_file: str = ".env") -> bool:
    """加载环境变量文件"""
    try:
        if not os.path.exists(env_file):
            logger.warning(f"环境变量文件不存在: {env_file}")
            return False

        with open(env_file, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    if "=" in line:
                        key, value = line.split("=", 1)
                        key = key.strip()
                        value = value.strip().strip('"').strip("'")
                        os.environ[key] = value

        logger.info(f"成功加载环境变量文件: {env_file}")
        # 重新加载配置
        config._load_config()
        return True

    except Exception as e:
        logger.error(f"加载环境变量文件失败: {e}")
        return False


def create_env_template(env_file: str = ".env.template") -> bool:
    """创建环境变量模板文件"""
    try:
        template_content = """# 数据库配置 - 106服务器
DB_HOST=***********
DB_PORT=5432
DB_NAME=product
DB_USER=username
DB_PASSWORD=password

# API配置
ZKMALL_API_BASE=https://zkmall.zktecoiot.com
ZKMALL_USERNAME=18929343717
ZKMALL_PASSWORD=your_api_password_here
API_TIMEOUT=30
API_RETRY_COUNT=3

# FastGPT配置
FASTGPT_API_BASE=https://api.fastgpt.in
FASTGPT_API_KEY=your_fastgpt_api_key_here
FASTGPT_TIMEOUT=30
FASTGPT_MAX_RETRIES=3

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# 同步配置
SYNC_INTERVAL=3600
SYNC_BATCH_SIZE=1000
SYNC_MAX_RETRIES=3
SYNC_TIMEOUT=300

# 系统配置
DEBUG=False
ENVIRONMENT=development
TIMEZONE=Asia/Shanghai
ENCODING=utf-8
"""

        with open(env_file, "w", encoding="utf-8") as f:
            f.write(template_content)

        logger.info(f"环境变量模板文件已创建: {env_file}")
        print(f"✅ 环境变量模板文件已创建: {env_file}")
        print("请根据实际情况修改配置值，然后重命名为 .env")
        return True

    except Exception as e:
        logger.error(f"创建环境变量模板文件失败: {e}")
        return False


if __name__ == "__main__":
    # 尝试加载环境变量文件
    load_env_file()

    # 设置日志
    setup_logging()

    # 打印配置摘要
    config.print_config_summary()

    # 验证配置
    if not config.validate_config():
        print("\n⚠️  配置验证失败，请检查环境变量设置")
        print("可以运行以下命令创建配置模板:")
        print("python config.py --create-template")
    else:
        print("\n✅ 配置验证通过，系统可以正常运行")

    # 如果命令行参数包含 --create-template，创建模板
    import sys

    if "--create-template" in sys.argv:
        create_env_template()
