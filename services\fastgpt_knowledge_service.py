"""
FastGPT知识库管理服务

该服务用于管理FastGPT知识库，包括创建知识库、上传文档、查询文档等功能。
严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import hashlib
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from config import Config

logger = logging.getLogger(__name__)


class FastGPTKnowledgeService:
    """FastGPT知识库管理服务"""

    def __init__(self):
        """初始化服务"""
        self.api_base = Config.FASTGPT_API_BASE
        self.api_token = Config.FASTGPT_API_TOKEN
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json",
        }

        if not self.api_token:
            logger.warning("FastGPT API Token未配置，某些功能可能不可用")

    def create_dataset(
        self,
        name: str,
        description: str = "",
        tags: List[str] = None,
        parent_id: str = "",
        avatar: str = "",
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        创建FastGPT知识库

        Args:
            name: 知识库名称
            description: 知识库描述
            tags: 标签列表
            parent_id: 父知识库ID
            avatar: 头像URL

        Returns:
            Tuple[是否成功, 知识库ID, 响应数据]
        """
        try:
            if not self.api_token:
                return False, "", {"error": "FastGPT API Token未配置"}

            # 构建请求数据
            payload = {
                "name": name,
                "type": "dataset",  # 知识库类型
            }

            if description:
                payload["intro"] = description

            if tags:
                payload["tags"] = tags

            if parent_id:
                payload["parentId"] = parent_id

            if avatar:
                payload["avatar"] = avatar

            # 发送创建请求
            url = f"{self.api_base}/api/core/dataset/create"

            logger.info(f"创建FastGPT知识库: {name}")
            response = requests.post(
                url, headers=self.headers, json=payload, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                dataset_id = result.get("data", {}).get("id", "")

                if dataset_id:
                    logger.info(f"FastGPT知识库创建成功: {name} (ID: {dataset_id})")
                    return True, dataset_id, result
                else:
                    logger.error(f"FastGPT知识库创建失败，未返回ID: {result}")
                    return False, "", result
            else:
                error_msg = (
                    f"FastGPT API请求失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, "", {"error": error_msg}

        except Exception as e:
            error_msg = f"创建FastGPT知识库异常: {str(e)}"
            logger.error(error_msg)
            return False, "", {"error": error_msg}

    def upload_text_data(
        self,
        dataset_id: str,
        content: str,
        title: str = "",
        source: str = "",
        metadata: Dict[str, Any] = None,
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        上传文本数据到知识库

        Args:
            dataset_id: 知识库ID
            content: 文本内容
            title: 文档标题
            source: 数据来源
            metadata: 元数据

        Returns:
            Tuple[是否成功, 数据ID, 响应数据]
        """
        try:
            if not self.api_token:
                return False, "", {"error": "FastGPT API Token未配置"}

            if not content.strip():
                return False, "", {"error": "文本内容不能为空"}

            # 构建请求数据
            payload = {
                "datasetId": dataset_id,
                "data": [
                    {
                        "q": title or "产品信息",  # 问题/标题
                        "a": content,  # 答案/内容
                        "source": source or "Excel导入",
                    }
                ],
            }

            if metadata:
                payload["data"][0]["metadata"] = metadata

            # 发送上传请求
            url = f"{self.api_base}/api/core/dataset/data/pushData"

            logger.info(f"上传文本数据到FastGPT知识库: {dataset_id}")
            response = requests.post(
                url, headers=self.headers, json=payload, timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data_id = result.get("data", {}).get("insertId", "")

                logger.info(f"文本数据上传成功: {title} (ID: {data_id})")
                return True, data_id, result
            else:
                error_msg = (
                    f"FastGPT数据上传失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, "", {"error": error_msg}

        except Exception as e:
            error_msg = f"上传文本数据异常: {str(e)}"
            logger.error(error_msg)
            return False, "", {"error": error_msg}

    def upload_markdown_data(
        self,
        dataset_id: str,
        markdown_content: str,
        title: str = "",
        source: str = "Excel转换",
        metadata: Dict[str, Any] = None,
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        上传Markdown格式数据到知识库

        Args:
            dataset_id: 知识库ID
            markdown_content: Markdown内容
            title: 文档标题
            source: 数据来源
            metadata: 元数据

        Returns:
            Tuple[是否成功, 数据ID, 响应数据]
        """
        return self.upload_text_data(
            dataset_id=dataset_id,
            content=markdown_content,
            title=title,
            source=source,
            metadata=metadata,
        )

    def get_dataset_list(
        self, parent_id: str = ""
    ) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        获取知识库列表

        Args:
            parent_id: 父知识库ID

        Returns:
            Tuple[是否成功, 知识库列表]
        """
        try:
            if not self.api_token:
                return False, []

            url = f"{self.api_base}/api/core/dataset/list"
            params = {}
            if parent_id:
                params["parentId"] = parent_id

            response = requests.get(
                url, headers=self.headers, params=params, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                datasets = result.get("data", [])
                logger.info(f"获取到 {len(datasets)} 个知识库")
                return True, datasets
            else:
                error_msg = (
                    f"获取知识库列表失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, []

        except Exception as e:
            error_msg = f"获取知识库列表异常: {str(e)}"
            logger.error(error_msg)
            return False, []

    def get_dataset_info(self, dataset_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        获取知识库详细信息

        Args:
            dataset_id: 知识库ID

        Returns:
            Tuple[是否成功, 知识库信息]
        """
        try:
            if not self.api_token:
                return False, {}

            url = f"{self.api_base}/api/core/dataset/detail"
            params = {"id": dataset_id}

            response = requests.get(
                url, headers=self.headers, params=params, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                dataset_info = result.get("data", {})
                logger.info(f"获取知识库信息成功: {dataset_id}")
                return True, dataset_info
            else:
                error_msg = (
                    f"获取知识库信息失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, {}

        except Exception as e:
            error_msg = f"获取知识库信息异常: {str(e)}"
            logger.error(error_msg)
            return False, {}

    def delete_dataset(self, dataset_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        删除知识库

        Args:
            dataset_id: 知识库ID

        Returns:
            Tuple[是否成功, 响应数据]
        """
        try:
            if not self.api_token:
                return False, {"error": "FastGPT API Token未配置"}

            url = f"{self.api_base}/api/core/dataset/delete"
            payload = {"id": dataset_id}

            response = requests.post(
                url, headers=self.headers, json=payload, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                logger.info(f"知识库删除成功: {dataset_id}")
                return True, result
            else:
                error_msg = f"删除知识库失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return False, {"error": error_msg}

        except Exception as e:
            error_msg = f"删除知识库异常: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}

    def search_dataset_data(
        self, dataset_id: str, query: str, limit: int = 10
    ) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        搜索知识库数据

        Args:
            dataset_id: 知识库ID
            query: 搜索查询
            limit: 结果数量限制

        Returns:
            Tuple[是否成功, 搜索结果列表]
        """
        try:
            if not self.api_token:
                return False, []

            url = f"{self.api_base}/api/core/dataset/searchTest"
            payload = {
                "datasetId": dataset_id,
                "text": query,
                "limit": limit,
            }

            response = requests.post(
                url, headers=self.headers, json=payload, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                search_results = result.get("data", [])
                logger.info(f"搜索到 {len(search_results)} 条结果")
                return True, search_results
            else:
                error_msg = (
                    f"搜索知识库数据失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, []

        except Exception as e:
            error_msg = f"搜索知识库数据异常: {str(e)}"
            logger.error(error_msg)
            return False, []

    def batch_upload_product_data(
        self,
        dataset_id: str,
        products_data: List[Dict[str, Any]],
        source: str = "Excel批量导入",
    ) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        批量上传产品数据到知识库

        Args:
            dataset_id: 知识库ID
            products_data: 产品数据列表
            source: 数据来源

        Returns:
            Tuple[是否成功, 数据ID列表, 结果统计]
        """
        success_ids = []
        failed_count = 0
        results = {
            "total": len(products_data),
            "success": 0,
            "failed": 0,
            "details": [],
        }

        for i, product in enumerate(products_data):
            try:
                # 转换产品数据为Markdown格式
                markdown_content = self._convert_product_to_markdown(product)

                title = f"产品信息 - {product.get('model', product.get('name', f'Product_{i+1}'))}"

                success, data_id, response = self.upload_markdown_data(
                    dataset_id=dataset_id,
                    markdown_content=markdown_content,
                    title=title,
                    source=source,
                    metadata={"product_model": product.get("model", ""), "index": i},
                )

                if success:
                    success_ids.append(data_id)
                    results["success"] += 1
                    logger.info(f"产品 {title} 上传成功")
                else:
                    failed_count += 1
                    results["failed"] += 1
                    logger.error(f"产品 {title} 上传失败: {response}")

                results["details"].append(
                    {
                        "index": i,
                        "title": title,
                        "success": success,
                        "data_id": data_id if success else "",
                        "error": response.get("error", "") if not success else "",
                    }
                )

            except Exception as e:
                failed_count += 1
                results["failed"] += 1
                error_msg = f"处理产品数据异常: {str(e)}"
                logger.error(error_msg)
                results["details"].append(
                    {
                        "index": i,
                        "title": f"Product_{i+1}",
                        "success": False,
                        "data_id": "",
                        "error": error_msg,
                    }
                )

        # 计算成功率
        success_rate = (
            (results["success"] / results["total"]) * 100 if results["total"] > 0 else 0
        )
        results["success_rate"] = round(success_rate, 2)

        logger.info(
            f"批量上传完成: 总数 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}, 成功率 {results['success_rate']}%"
        )

        return len(success_ids) > 0, success_ids, results

    def _convert_product_to_markdown(self, product: Dict[str, Any]) -> str:
        """
        将产品数据转换为Markdown格式

        Args:
            product: 产品数据字典

        Returns:
            Markdown格式的产品信息
        """
        markdown_lines = []

        # 产品标题
        name = product.get("name", product.get("productName", "未知产品"))
        model = product.get("model", product.get("productModel", ""))

        if model:
            markdown_lines.append(f"# {name} (型号: {model})")
        else:
            markdown_lines.append(f"# {name}")

        markdown_lines.append("")

        # 基本信息
        markdown_lines.append("## 基本信息")
        markdown_lines.append("")

        # 产品型号
        if model:
            markdown_lines.append(f"**产品型号**: {model}")

        # 产品名称
        markdown_lines.append(f"**产品名称**: {name}")

        # 分类信息
        if product.get("categoryName"):
            markdown_lines.append(f"**产品分类**: {product.get('categoryName')}")

        # 品牌信息
        if product.get("brandName"):
            markdown_lines.append(f"**品牌**: {product.get('brandName')}")

        # 价格信息
        if product.get("price"):
            markdown_lines.append(f"**价格**: ¥{product.get('price')}")

        markdown_lines.append("")

        # 产品描述
        if product.get("description") or product.get("detail"):
            markdown_lines.append("## 产品描述")
            markdown_lines.append("")
            description = product.get("description", product.get("detail", ""))
            markdown_lines.append(description)
            markdown_lines.append("")

        # 规格参数
        if product.get("specification") or product.get("spec"):
            markdown_lines.append("## 规格参数")
            markdown_lines.append("")
            spec = product.get("specification", product.get("spec", ""))
            markdown_lines.append(spec)
            markdown_lines.append("")

        # 应用场景
        if product.get("application"):
            markdown_lines.append("## 应用场景")
            markdown_lines.append("")
            markdown_lines.append(product.get("application"))
            markdown_lines.append("")

        # 技术特性
        if product.get("features"):
            markdown_lines.append("## 技术特性")
            markdown_lines.append("")
            features = product.get("features")
            if isinstance(features, list):
                for feature in features:
                    markdown_lines.append(f"- {feature}")
            else:
                markdown_lines.append(features)
            markdown_lines.append("")

        # 其他信息
        other_fields = {
            "manufacturer": "制造商",
            "warranty": "质保信息",
            "certification": "认证信息",
            "weight": "重量",
            "dimensions": "尺寸",
            "power": "功率",
            "voltage": "电压",
            "temperature": "工作温度",
            "humidity": "工作湿度",
        }

        other_info = []
        for field, label in other_fields.items():
            if product.get(field):
                other_info.append(f"**{label}**: {product.get(field)}")

        if other_info:
            markdown_lines.append("## 其他信息")
            markdown_lines.append("")
            markdown_lines.extend(other_info)
            markdown_lines.append("")

        # 更新时间
        markdown_lines.append("---")
        markdown_lines.append(
            f"*数据更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
        )

        return "\n".join(markdown_lines)
