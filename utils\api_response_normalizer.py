"""
修复后的API响应格式标准化工具

该模块修复了原有的normalize方法调用问题，确保所有同步服务都能正确使用标准化功能。
现在集成了增强的附件处理功能，可以正确提取 file= 参数和分类数据。
"""

import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)

# Import enhanced processor for attachment and category handling
try:
    from .enhanced_attachment_processor import enhanced_processor

    ENHANCED_PROCESSING_AVAILABLE = True
    logger.info("Enhanced attachment processing enabled")
except ImportError:
    ENHANCED_PROCESSING_AVAILABLE = False
    logger.warning("Enhanced attachment processing not available")


class ApiResponseNormalizer:
    """修复后的API响应格式标准化器"""

    # 预定义的字段映射表
    FIELD_MAPPINGS = {
        "product": {
            # API字段名 -> 数据库字段名
            "name": "name",
            "productName": "name",
            "category": "category_id",
            "categoryName": "category_name",
            "label": "label_id",
            "labelList": "labels",
            "labelName": "label_name",
            "smallImg": "small_img",
            "banner": "banner",
            "spec": "spec",
            "introduction": "introduction",
            "details": "details",
            "paramInfo": "param_info",
            "parameterInfo": "param_info",
            "newParam": "new_param",
            "paramInfoList": "param_info_list",
            "useTo": "use_to",
            "usage": "use_to",
            "showFor": "show_for",
            "commonProblem": "common_problem",
            "commonIssues": "common_problem",
            "instructions": "instructions",
            "instructionIds": "instructions",
            "other": "other",
            "attachments": "other",
            "guide": "guide",
            "operationGuide": "guide",
            "qualifications": "qualifications",
            "qualificationUrl": "qualifications",
            "video": "video_url",
            "videoUrl": "video_url",
            "videoExplanation": "video_explanation",
            "videoInstallation": "video_installation",
            "videoTroubleshooting": "video_troubleshooting",
            "count": "view_count",
            "viewCount": "view_count",
            "likeCount": "like_count",
            "favoriteCount": "favorite_count",
            "isSuggest": "is_recommended",
            "status": "status",
            "price": "price",
            "siteId": "site_id",
            "companyId": "company_id",
            "brandId": "brand_id",
            "brandName": "brand_name",
            "createTime": "created_at",
            "createdAt": "created_at",
            "updateTime": "updated_at",
            "updatedAt": "updated_at",
        },
        "case": {
            "caseName": "title",
            "name": "title",
            "category": "categoryId",
            "categoryName": "categoryName",
            "keywords": "tags",
            "introduction": "summary",
            "content": "description",
            "details": "details",
            "img": "image",
            "banner": "bannerImage",
            "smallImg": "thumbnail",
            "video": "videoUrl",
            "publishName": "publisher",
            "realName": "realPublisher",
            "publishId": "publisherId",
            "companyId": "companyId",
            "companyName": "companyName",
            "province": "province",
            "city": "city",
            "county": "district",
            "productId": "productId",
            "productName": "productName",
            "count": "viewCount",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            "approveStatus": "approvalStatus",
            "isSuggest": "isRecommended",
            "isAuth": "isAuthenticated",
            "status": "status",
            "siteId": "siteId",
            "createTime": "createdAt",
            "updateTime": "updatedAt",
        },
        "programme": {
            "id": "id",
            "name": "title",
            "programmeName": "title",
            "categoryId": "categoryId",
            "categoryName": "categoryName",
            "categorySceneId": "categorySceneId",
            "categorySceneName": "categorySceneName",
            "introduction": "description",
            "content": "content",
            "details": "details",
            "banner": "bannerImage",
            "smallImg": "thumbnail",
            "video": "videoUrl",
            "count": "viewCount",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            "isSuggest": "isRecommended",
            "status": "status",
            "siteId": "siteId",
            "createTime": "createdAt",
            "updateTime": "updatedAt",
        },
        "information": {
            "id": "id",
            "title": "title",
            "details": "content",
            "categoryId": "categoryId",
            "categoryName": "categoryName",
            "picVideo": "coverMedia",
            "smallImg": "thumbnail",
            "videoUrl": "videoUrl",
            "otherUrl": "attachmentUrl",
            "watch": "viewCount",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            "isSuggest": "isRecommended",
            "isHot": "isHot",
            "top": "isTop",
            "status": "status",
            "productId": "productId",
            "siteId": "siteId",
            "createTime": "createdAt",
            "updateTime": "updatedAt",
        },
        "distribution_order": {
            "id": "id",
            "name": "title",
            "status": "status",
            "type": "type",
            "customerName": "customerName",
            "phone": "phone",
            "contacts": "contacts",
            "companyId": "companyId",
            "companyName": "companyName",
            "userId": "userId",
            "hidePrice": "isPriceHidden",
            "firCategoryId": "primaryCategoryId",
            "firCategoryName": "primaryCategoryName",
            "secCategoryId": "secondaryCategoryId",
            "secCategoryName": "secondaryCategoryName",
            "sourceType": "sourceType",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            "siteId": "siteId",
            "createTime": "createdAt",
            "updateTime": "updatedAt",
        },
    }

    def __init__(self):
        """初始化标准化器"""
        logger.info("ApiResponseNormalizer初始化完成")

    def normalize(self, response: Any, data_type: str = "generic") -> Any:
        """
        标准化API响应数据 - 实例方法

        Args:
            response: API响应数据
            data_type: 数据类型

        Returns:
            标准化后的数据
        """
        try:
            if not response:
                logger.warning("响应数据为空")
                return []

            # 获取字段映射
            field_mapping = self.FIELD_MAPPINGS.get(data_type, {})

            # 判断响应类型并处理
            if isinstance(response, list):
                return self._normalize_list_data(response, field_mapping)
            elif isinstance(response, dict):
                # 检查是否是标准API响应格式
                if self._is_api_response_format(response):
                    return self._normalize_api_response(response, field_mapping)
                else:
                    # 单个对象
                    normalized = self._normalize_single_item(response, field_mapping)
                    return [normalized] if normalized else []
            else:
                logger.warning(f"未知的响应类型: {type(response)}")
                return []

        except Exception as e:
            logger.error(f"数据标准化失败: {e}")
            return response if isinstance(response, list) else []

    def _is_api_response_format(self, response: Dict[str, Any]) -> bool:
        """判断是否是标准API响应格式"""
        return any(key in response for key in ["data", "rows", "total", "code"])

    def _normalize_api_response(
        self, response: Dict[str, Any], field_mapping: Dict[str, str]
    ) -> List[Dict[str, Any]]:
        """标准化API响应格式的数据"""
        try:
            # 检查错误响应
            if "code" in response and response.get("code") != 200:
                logger.warning(f"API返回错误: {response}")
                return []

            # 提取数据 - 支持多种格式
            data = None

            # 1. 标准格式: {"code": 200, "data": {"rows": [...]}}
            if "data" in response:
                data = response["data"]
                if isinstance(data, dict) and "rows" in data:
                    data = data["rows"]
                elif isinstance(data, list):
                    data = data

            # 2. 新格式: {"products": [...], "pagination": {...}}
            elif "products" in response:
                data = response["products"]
                logger.debug(
                    f"从products字段提取到 {len(data) if isinstance(data, list) else 0} 条数据"
                )

            # 3. 直接格式: {"rows": [...]}
            elif "rows" in response:
                data = response["rows"]

            # 4. 其他可能的字段名
            elif any(
                field in response for field in ["records", "list", "items", "content"]
            ):
                for field in ["records", "list", "items", "content"]:
                    if field in response and isinstance(response[field], list):
                        data = response[field]
                        logger.debug(f"从{field}字段提取到 {len(data)} 条数据")
                        break

            # 5. 列表格式: [...]
            elif isinstance(response, list):
                data = response

            if not data:
                logger.warning(
                    f"未能从响应中提取到数据，响应字段: {list(response.keys()) if isinstance(response, dict) else type(response)}"
                )
                return []

            return self._normalize_list_data(data, field_mapping)

        except Exception as e:
            logger.error(f"标准化API响应失败: {e}")
            return []

    def _normalize_list_data(
        self, data_list: List[Dict[str, Any]], field_mapping: Dict[str, str]
    ) -> List[Dict[str, Any]]:
        """标准化列表数据"""
        if not isinstance(data_list, list):
            return []

        normalized_list = []
        for item in data_list:
            if isinstance(item, dict):
                normalized_item = self._normalize_single_item(item, field_mapping)
                if normalized_item:
                    normalized_list.append(normalized_item)

        logger.info(f"成功标准化 {len(normalized_list)} 条数据")
        return normalized_list

    def _normalize_single_item(
        self, item: Dict[str, Any], field_mapping: Dict[str, str]
    ) -> Optional[Dict[str, Any]]:
        """标准化单个数据项"""
        try:
            if not isinstance(item, dict) or not item:
                return None

            normalized_item = {}

            # 应用字段映射
            for original_key, value in item.items():
                if value is None:
                    continue

                # 使用映射的字段名或原字段名
                mapped_key = field_mapping.get(original_key, original_key)
                normalized_item[mapped_key] = self._clean_field_value(value)

            # 添加必要的默认值
            if "id" not in normalized_item and ("id" in item):
                normalized_item["id"] = item["id"]

            return normalized_item

        except Exception as e:
            logger.error(f"标准化单个数据项失败: {e}")
            return None

    def _clean_field_value(self, value: Any) -> Any:
        """清理字段值"""
        try:
            if value is None:
                return None
            elif isinstance(value, str):
                # 清理字符串
                cleaned = value.strip()
                return cleaned if cleaned else None
            elif isinstance(value, (int, float, bool)):
                return value
            elif isinstance(value, (list, dict)):
                return value
            else:
                return str(value)
        except Exception:
            return None

    # 静态方法，保持向后兼容
    @staticmethod
    def normalize_list_response(
        response: Any,
        field_mapping: Optional[Dict[str, str]] = None,
        data_type: str = "generic",
    ) -> List[Dict[str, Any]]:
        """静态方法：标准化列表响应"""
        normalizer = ApiResponseNormalizer()

        # 如果提供了自定义映射，临时使用
        if field_mapping:
            original_mapping = normalizer.FIELD_MAPPINGS.get(data_type, {})
            normalizer.FIELD_MAPPINGS[data_type] = {**original_mapping, **field_mapping}

        result = normalizer.normalize(response, data_type)
        return result if isinstance(result, list) else [result] if result else []

    @staticmethod
    def normalize_single_response(
        response: Any,
        field_mapping: Optional[Dict[str, str]] = None,
        data_type: str = "generic",
    ) -> Optional[Dict[str, Any]]:
        """静态方法：标准化单个响应"""
        normalizer = ApiResponseNormalizer()

        # 如果提供了自定义映射，临时使用
        if field_mapping:
            original_mapping = normalizer.FIELD_MAPPINGS.get(data_type, {})
            normalizer.FIELD_MAPPINGS[data_type] = {**original_mapping, **field_mapping}

        result = normalizer.normalize(response, data_type)
        if isinstance(result, list) and len(result) > 0:
            return result[0]
        return None

    @staticmethod
    def handle_api_error(response: Any) -> Dict[str, Any]:
        """处理API错误响应"""
        try:
            if isinstance(response, dict):
                code = response.get("code")
                message = response.get("message", response.get("msg", ""))

                error_info = {
                    "success": False,
                    "error_code": code,
                    "error_message": message,
                    "data": [],
                }

                # 特殊错误处理
                if code == 500 and "NumberFormatException" in message:
                    error_info["error_type"] = "parameter_format_error"
                elif code == 401:
                    error_info["error_type"] = "authentication_error"
                else:
                    error_info["error_type"] = "unknown_error"

                return error_info

            return {
                "success": False,
                "error_message": "Unknown response format",
                "data": [],
            }

        except Exception as e:
            logger.error(f"处理API错误失败: {e}")
            return {"success": False, "error_message": str(e), "data": []}

    @staticmethod
    def normalize_pagination_response(response: Any) -> Dict[str, Any]:
        """
        标准化分页响应

        Args:
            response: 原始API响应

        Returns:
            标准化的分页信息
        """
        pagination = {
            "total": 0,
            "current": 1,
            "size": 20,
            "pages": 0,
            "hasNext": False,
            "hasPrevious": False,
        }

        try:
            # 从标准格式提取
            if (
                isinstance(response, dict)
                and response.get("code") == 200
                and "data" in response
            ):
                data = response["data"]
                if isinstance(data, dict):
                    pagination.update(
                        {
                            "total": data.get("total", 0),
                            "current": data.get("current", 1),
                            "size": data.get("size", data.get("pageSize", 20)),
                            "pages": data.get("pages", 0),
                        }
                    )

            # 从直接格式提取
            elif isinstance(response, dict):
                pagination.update(
                    {
                        "total": response.get("total", 0),
                        "current": response.get("current", 1),
                        "size": response.get("size", response.get("pageSize", 20)),
                        "pages": response.get("pages", 0),
                    }
                )

            # 计算总页数和导航信息
            if pagination["total"] > 0 and pagination["size"] > 0:
                pagination["pages"] = (
                    pagination["total"] + pagination["size"] - 1
                ) // pagination["size"]
                pagination["hasNext"] = pagination["current"] < pagination["pages"]
                pagination["hasPrevious"] = pagination["current"] > 1

            logger.debug(f"分页信息标准化完成: {pagination}")
            return pagination

        except Exception as e:
            logger.error(f"分页信息标准化失败: {e}")
            return pagination


# 向后兼容的全局函数
def normalize_api_response(
    response: Any, data_type: str = "generic"
) -> List[Dict[str, Any]]:
    """全局函数：标准化API响应"""
    return ApiResponseNormalizer.normalize_list_response(response, data_type=data_type)


def normalize_single_api_response(
    response: Any, data_type: str = "generic"
) -> Optional[Dict[str, Any]]:
    """全局函数：标准化单个API响应"""
    return ApiResponseNormalizer.normalize_single_response(
        response, data_type=data_type
    )


def safe_api_call(api_func, *args, **kwargs):
    """安全的API调用包装器"""
    try:
        response = api_func(*args, **kwargs)

        # 检查错误响应
        if isinstance(response, dict) and response.get("code") != 200:
            return ApiResponseNormalizer.handle_api_error(response)

        return response

    except Exception as e:
        logger.error(f"API调用失败: {e}")
        return {"success": False, "error_message": str(e), "data": []}


def get_pagination_info(response: Any) -> Dict[str, Any]:
    """
    便捷的分页信息获取函数

    Args:
        response: API响应

    Returns:
        分页信息
    """
    return ApiResponseNormalizer.normalize_pagination_response(response)
